/**
 * KaTeX Integration Module
 * Handles math equation rendering and editing capabilities
 */

class KaTeXIntegration {
  constructor() {
    this.isInitialized = false;
    this.mathDelimiters = [
      {left: '$$', right: '$$', display: true},
      {left: '$', right: '$', display: false},
      {left: '\\(', right: '\\)', display: false},
      {left: '\\[', right: '\\]', display: true}
    ];
    this.macros = {}; // Shared macros across equations
    this.initialize();
  }

  /**
   * Initialize KaTeX integration
   */
  initialize() {
    console.log('Initializing KaTeX integration...');
    console.log('katex available:', typeof katex !== 'undefined');
    console.log('renderMathInElement available:', typeof renderMathInElement !== 'undefined');

    if (typeof katex === 'undefined') {
      console.error('KaTeX library not loaded');
      return;
    }

    if (typeof renderMathInElement === 'undefined') {
      console.error('KaTeX auto-render extension not loaded');
      return;
    }

    this.isInitialized = true;
    console.log('KaTeX integration initialized successfully');
  }

  /**
   * Check if KaTeX is available and initialized
   */
  isAvailable() {
    return this.isInitialized && typeof katex !== 'undefined' && typeof renderMathInElement !== 'undefined';
  }

  /**
   * Render math in an element using auto-render
   * @param {HTMLElement} element - Element to render math in
   * @param {Object} options - Additional options
   */
  renderMathInElement(element, options = {}) {
    if (!this.isAvailable()) {
      console.warn('KaTeX not available for rendering');
      return;
    }

    try {
      const renderOptions = {
        delimiters: this.mathDelimiters,
        throwOnError: false,
        errorColor: '#cc0000',
        macros: this.macros,
        ignoredTags: ['script', 'noscript', 'style', 'textarea', 'pre', 'code', 'option'],
        ignoredClasses: ['katex-editing'],
        ...options
      };

      renderMathInElement(element, renderOptions);
    } catch (error) {
      console.error('Error rendering math:', error);
    }
  }

  /**
   * Render a single math expression
   * @param {string} expression - Math expression to render
   * @param {boolean} displayMode - Whether to render in display mode
   * @param {Object} options - Additional options
   * @returns {string} Rendered HTML
   */
  renderMath(expression, displayMode = false, options = {}) {
    if (!this.isAvailable()) {
      return `<span style="color: red;">[Math rendering unavailable]</span>`;
    }

    try {
      const renderOptions = {
        displayMode,
        throwOnError: false,
        errorColor: '#cc0000',
        macros: this.macros,
        ...options
      };

      return katex.renderToString(expression, renderOptions);
    } catch (error) {
      console.error('Error rendering math expression:', error);
      return `<span style="color: red;" title="${error.message}">[Math Error: ${expression}]</span>`;
    }
  }

  /**
   * Extract math expressions from text
   * @param {string} text - Text to extract math from
   * @returns {Array} Array of math expressions with positions
   */
  extractMathExpressions(text) {
    const expressions = [];

    // Process delimiters in order of priority (longer delimiters first)
    const sortedDelimiters = [...this.mathDelimiters].sort((a, b) => b.left.length - a.left.length);

    for (const delimiter of sortedDelimiters) {
      const leftEscaped = this.escapeRegExp(delimiter.left);
      const rightEscaped = this.escapeRegExp(delimiter.right);
      const regex = new RegExp(leftEscaped + '([\\s\\S]*?)' + rightEscaped, 'g');

      let match;
      while ((match = regex.exec(text)) !== null) {
        // Check if this position is already covered by a longer delimiter
        const isOverlapping = expressions.some(expr =>
          (match.index >= expr.start && match.index < expr.end) ||
          (match.index + match[0].length > expr.start && match.index + match[0].length <= expr.end)
        );

        if (!isOverlapping) {
          expressions.push({
            full: match[0],
            expression: match[1].trim(),
            start: match.index,
            end: match.index + match[0].length,
            displayMode: delimiter.display,
            delimiter: delimiter
          });
        }
      }
    }

    // Sort by position to handle overlapping matches
    return expressions.sort((a, b) => a.start - b.start);
  }

  /**
   * Replace math expressions in text with placeholders
   * @param {string} text - Text to process
   * @returns {Object} Object with processed text and math expressions
   */
  protectMathExpressions(text) {
    const expressions = this.extractMathExpressions(text);
    let processedText = text;
    const mathPlaceholders = [];

    // Replace from end to start to maintain positions
    for (let i = expressions.length - 1; i >= 0; i--) {
      const expr = expressions[i];
      const placeholder = `__MATH_PLACEHOLDER_${i}__`;
      
      processedText = processedText.substring(0, expr.start) + 
                    placeholder + 
                    processedText.substring(expr.end);
      
      mathPlaceholders.unshift({
        placeholder,
        expression: expr.expression,
        displayMode: expr.displayMode,
        full: expr.full
      });
    }

    return {
      text: processedText,
      mathExpressions: mathPlaceholders
    };
  }

  /**
   * Restore math expressions from placeholders
   * @param {string} html - HTML with placeholders
   * @param {Array} mathExpressions - Array of math expressions
   * @returns {string} HTML with rendered math
   */
  restoreMathExpressions(html, mathExpressions) {
    let processedHtml = html;

    mathExpressions.forEach((math, index) => {
      const placeholder = `__MATH_PLACEHOLDER_${index}__`;
      const renderedMath = this.renderMath(math.expression, math.displayMode);
      processedHtml = processedHtml.replace(placeholder, renderedMath);
    });

    return processedHtml;
  }

  /**
   * Insert equation at cursor position
   * @param {HTMLElement} editor - Editor element
   * @param {boolean} displayMode - Whether to insert display mode equation
   */
  insertEquation(editor, displayMode = false) {
    const delimiter = displayMode ? '$$' : '$';
    const placeholder = displayMode ? 'E = mc^2' : 'x^2';
    const equation = `${delimiter}${placeholder}${delimiter}`;
    
    if (window.EditorUtils && window.EditorUtils.insertTextAtCursor) {
      window.EditorUtils.insertTextAtCursor(editor, equation);
    } else {
      // Fallback insertion method
      const selection = window.getSelection();
      if (selection.rangeCount > 0) {
        const range = selection.getRangeAt(0);
        range.deleteContents();
        range.insertNode(document.createTextNode(equation));
        range.collapse(false);
      }
    }
  }

  /**
   * Check if cursor is inside a math expression
   * @param {HTMLElement} editor - Editor element
   * @returns {Object|null} Math expression info or null
   */
  getMathAtCursor(editor) {
    const selection = window.getSelection();
    if (selection.rangeCount === 0) return null;

    const range = selection.getRangeAt(0);
    const text = editor.textContent || editor.innerText;
    const cursorPos = this.getCursorPosition(editor, range);

    const expressions = this.extractMathExpressions(text);
    
    for (const expr of expressions) {
      if (cursorPos >= expr.start && cursorPos <= expr.end) {
        return expr;
      }
    }

    return null;
  }

  /**
   * Get cursor position in text
   * @param {HTMLElement} editor - Editor element
   * @param {Range} range - Selection range
   * @returns {number} Cursor position
   */
  getCursorPosition(editor, range) {
    const preCaretRange = range.cloneRange();
    preCaretRange.selectNodeContents(editor);
    preCaretRange.setEnd(range.endContainer, range.endOffset);
    return preCaretRange.toString().length;
  }

  /**
   * Escape special regex characters
   * @param {string} string - String to escape
   * @returns {string} Escaped string
   */
  escapeRegExp(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }

  /**
   * Add custom macro
   * @param {string} name - Macro name
   * @param {string} definition - Macro definition
   */
  addMacro(name, definition) {
    this.macros[name] = definition;
  }

  /**
   * Remove macro
   * @param {string} name - Macro name
   */
  removeMacro(name) {
    delete this.macros[name];
  }

  /**
   * Get available math functions for autocomplete
   * @returns {Array} Array of function names
   */
  getMathFunctions() {
    return [
      'frac', 'sqrt', 'sum', 'int', 'lim', 'sin', 'cos', 'tan', 'log', 'ln',
      'alpha', 'beta', 'gamma', 'delta', 'epsilon', 'theta', 'lambda', 'mu',
      'pi', 'sigma', 'phi', 'psi', 'omega', 'infty', 'partial', 'nabla',
      'cdot', 'times', 'div', 'pm', 'mp', 'leq', 'geq', 'neq', 'approx',
      'equiv', 'subset', 'supset', 'in', 'notin', 'cup', 'cap', 'emptyset'
    ];
  }
}

// Create global instance
window.katexIntegration = new KaTeXIntegration();
