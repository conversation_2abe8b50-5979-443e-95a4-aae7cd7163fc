/**
 * File Manager Module
 * Handles file operations like open, save, export
 */

class FileManager {
  constructor() {
    this.fileInput = null;
    this.directoryFileInput = null;
    this.currentFile = null;
    this.currentDirectory = null;
    this.recentFiles = [];
    this.recentDirectories = [];
    this.maxRecentFiles = 10;
    this.maxRecentDirectories = 5;
    this.directoryHandle = null; // For File System Access API

    this.initialize();
  }

  /**
   * Initialize file manager
   */
  initialize() {
    this.fileInput = document.getElementById('file-input');
    this.directoryFileInput = document.getElementById('directory-file-input');

    if (!this.fileInput) {
      console.error('File input element not found');
      return;
    }

    this.setupEventListeners();
    this.loadRecentFiles();
    this.loadRecentDirectories();
    this.setupKeyboardShortcuts();
    this.setupDirectoryModal();
  }

  /**
   * Setup event listeners
   */
  setupEventListeners() {
    // File input change event
    this.fileInput.addEventListener('change', this.handleFileSelect.bind(this));

    // Directory input change event
    if (this.directoryFileInput) {
      this.directoryFileInput.addEventListener('change', this.handleDirectorySelect.bind(this));
    }

    // Drag and drop events
    document.addEventListener('dragover', this.handleDragOver.bind(this));
    document.addEventListener('drop', this.handleDrop.bind(this));

    // Before unload warning for unsaved changes
    window.addEventListener('beforeunload', this.handleBeforeUnload.bind(this));
  }

  /**
   * Setup keyboard shortcuts
   */
  setupKeyboardShortcuts() {
    EditorUtils.addKeyboardShortcut('ctrl+n', () => this.newFile());
    EditorUtils.addKeyboardShortcut('ctrl+o', () => this.openFile());
    EditorUtils.addKeyboardShortcut('ctrl+s', () => this.saveFile());
    EditorUtils.addKeyboardShortcut('ctrl+shift+s', () => this.saveAsFile());
    EditorUtils.addKeyboardShortcut('ctrl+alt+s', () => this.saveToDirectory());
    EditorUtils.addKeyboardShortcut('ctrl+shift+e', () => this.exportHtml());
  }

  /**
   * Create new file
   */
  newFile() {
    if (window.editor.isModified) {
      const shouldSave = EditorUtils.showConfirmDialog(
        'You have unsaved changes. Do you want to save before creating a new file?'
      );
      
      if (shouldSave) {
        this.saveFile();
      }
    }

    window.editor.setContent('');
    window.editor.setFileName('Untitled.md');
    window.editor.markAsModified(false);
    this.currentFile = null;
    this.clearCurrentDirectory();

    // Focus editor
    window.editor.focus();
  }

  /**
   * Open file dialog
   */
  async openFile() {
    if (window.editor.isModified) {
      const shouldSave = EditorUtils.showConfirmDialog(
        'You have unsaved changes. Do you want to save before opening a new file?'
      );

      if (shouldSave) {
        await this.saveFile();
      }
    }

    // Try to use File System Access API first
    if ('showOpenFilePicker' in window) {
      try {
        await this.openFileWithModernDialog();
        return;
      } catch (error) {
        if (error.name !== 'AbortError') {
          console.error('Error with modern file picker:', error);
        }
        // Fall back to traditional method
      }
    }

    // Fallback to traditional file input
    this.fileInput.click();
  }

  /**
   * Open file using File System Access API
   */
  async openFileWithModernDialog() {
    const [fileHandle] = await window.showOpenFilePicker({
      types: [
        {
          description: 'Markdown files',
          accept: {
            'text/markdown': ['.md'],
            'text/plain': ['.txt', '.markdown']
          }
        }
      ]
    });

    const file = await fileHandle.getFile();
    await this.loadFileWithHandle(file, fileHandle);
  }

  /**
   * Handle file selection (fallback method)
   */
  async handleFileSelect(event) {
    const file = event.target.files[0];
    if (file) {
      await this.loadFile(file);
    }

    // Reset file input
    this.fileInput.value = '';
  }

  /**
   * Load file content with file handle (for File System Access API)
   */
  async loadFileWithHandle(file, fileHandle) {
    try {
      const content = await EditorUtils.readFileAsText(file);

      window.editor.setContent(content);
      window.editor.setFileName(file.name);
      window.editor.markAsModified(false);

      this.currentFile = {
        name: file.name,
        content: content,
        lastModified: file.lastModified,
        handle: fileHandle
      };

      this.addToRecentFiles(file.name);

      // Focus editor
      window.editor.focus();

    } catch (error) {
      console.error('Error loading file:', error);
      alert('Error loading file: ' + error.message);
    }
  }

  /**
   * Load file content (fallback method)
   */
  async loadFile(file) {
    try {
      const content = await EditorUtils.readFileAsText(file);

      window.editor.setContent(content);
      window.editor.setFileName(file.name);
      window.editor.markAsModified(false);

      this.currentFile = {
        name: file.name,
        content: content,
        lastModified: file.lastModified
      };

      this.addToRecentFiles(file.name);

      // Focus editor
      window.editor.focus();

    } catch (error) {
      console.error('Error loading file:', error);
      alert('Error loading file: ' + error.message);
    }
  }

  /**
   * Save current file
   */
  async saveFile() {
    const content = window.editor.getContent();
    const filename = window.editor.currentFileName;

    // Check if this is a new file (never saved before)
    const isNewFile = !this.currentFile || filename === 'Untitled.md' || filename.startsWith('Untitled');

    if (isNewFile) {
      // For new files, show save dialog
      await this.saveFileWithDialog();
    } else {
      // For existing files, auto-save
      this.saveExistingFile();
    }
  }

  /**
   * Save new file with dialog
   */
  async saveFileWithDialog() {
    try {
      // Check if File System Access API is supported
      if ('showSaveFilePicker' in window) {
        await this.saveFileWithModernDialog();
      } else {
        // Fallback to traditional download
        this.saveFileWithDownload();
      }
    } catch (error) {
      if (error.name !== 'AbortError') {
        console.error('Error saving file:', error);
        // Fallback to download
        this.saveFileWithDownload();
      }
    }
  }

  /**
   * Save file using File System Access API
   */
  async saveFileWithModernDialog() {
    const content = window.editor.getContent();
    const filename = window.editor.currentFileName;

    const fileHandle = await window.showSaveFilePicker({
      suggestedName: filename,
      types: [
        {
          description: 'Markdown files',
          accept: {
            'text/markdown': ['.md'],
            'text/plain': ['.txt']
          }
        }
      ]
    });

    const writable = await fileHandle.createWritable();
    await writable.write(content);
    await writable.close();

    // Update current file info
    const savedFile = await fileHandle.getFile();
    window.editor.setFileName(savedFile.name);
    window.editor.markAsModified(false);

    this.currentFile = {
      name: savedFile.name,
      content: content,
      lastModified: Date.now(),
      handle: fileHandle
    };

    this.addToRecentFiles(savedFile.name);
  }

  /**
   * Save file with traditional download
   */
  saveFileWithDownload() {
    const content = window.editor.getContent();
    const filename = window.editor.currentFileName;

    EditorUtils.downloadFile(content, filename, 'text/markdown');

    window.editor.markAsModified(false);

    this.currentFile = {
      name: filename,
      content: content,
      lastModified: Date.now()
    };

    this.addToRecentFiles(filename);
  }

  /**
   * Save existing file automatically
   */
  async saveExistingFile() {
    const content = window.editor.getContent();

    // If we have a file handle from File System Access API, use it
    if (this.currentFile && this.currentFile.handle) {
      try {
        const writable = await this.currentFile.handle.createWritable();
        await writable.write(content);
        await writable.close();

        window.editor.markAsModified(false);
        this.currentFile.content = content;
        this.currentFile.lastModified = Date.now();

        return;
      } catch (error) {
        console.error('Error saving with file handle:', error);
        // Fall back to download
      }
    }

    // Fallback to download for existing files
    this.saveFileWithDownload();
  }

  /**
   * Save file with new name
   */
  saveAsFile() {
    const currentName = window.editor.currentFileName;
    const newName = EditorUtils.showPromptDialog('Enter filename:', currentName);

    if (newName) {
      window.editor.setFileName(newName);
      this.saveFile();
    }
  }

  /**
   * Open file from a specific directory
   */
  async openFromDirectory() {
    // Check for unsaved changes first
    if (window.editor.isModified) {
      const shouldSave = EditorUtils.showConfirmDialog(
        'You have unsaved changes. Do you want to save before opening a new file?'
      );

      if (shouldSave) {
        this.saveFile();
      }
    }

    try {
      // Check if File System Access API is supported
      if ('showDirectoryPicker' in window) {
        await this.openFromDirectoryModern();
      } else {
        // Fallback to traditional directory input
        this.openFromDirectoryFallback();
      }
    } catch (error) {
      console.error('Error opening from directory:', error);
      // Fallback to traditional method
      this.openFromDirectoryFallback();
    }
  }

  /**
   * Open from directory using File System Access API
   */
  async openFromDirectoryModern() {
    try {
      this.directoryHandle = await window.showDirectoryPicker();
      const files = await this.getMarkdownFilesFromDirectory(this.directoryHandle);

      if (files.length === 0) {
        alert('No markdown files found in the selected directory.');
        return;
      }

      // Show file selection dialog
      const selectedFile = await this.showFileSelectionDialog(files);
      if (selectedFile) {
        const fileHandle = selectedFile.handle;
        const file = await fileHandle.getFile();
        await this.loadFileWithHandle(file, fileHandle);

        this.currentDirectory = {
          name: this.directoryHandle.name,
          handle: this.directoryHandle
        };
        this.addToRecentDirectories(this.directoryHandle.name);
        this.updateDirectoryInfo();
      }
    } catch (error) {
      if (error.name !== 'AbortError') {
        console.error('Error with directory picker:', error);
        throw error;
      }
    }
  }

  /**
   * Fallback method for opening from directory
   */
  openFromDirectoryFallback() {
    if (this.directoryFileInput) {
      this.directoryFileInput.click();
    }
  }

  /**
   * Save file to a specific directory
   */
  async saveToDirectory() {
    try {
      // Check if File System Access API is supported
      if ('showDirectoryPicker' in window) {
        await this.saveToDirectoryModern();
      } else {
        // Fallback to regular save
        alert('Directory selection is not supported in this browser. Using regular save instead.');
        this.saveFile();
      }
    } catch (error) {
      console.error('Error saving to directory:', error);
      // Fallback to regular save
      this.saveFile();
    }
  }

  /**
   * Save to directory using File System Access API
   */
  async saveToDirectoryModern() {
    try {
      const directoryHandle = await window.showDirectoryPicker();
      const content = window.editor.getContent();
      const filename = window.editor.currentFileName;

      // Create or get file handle
      const fileHandle = await directoryHandle.getFileHandle(filename, { create: true });
      const writable = await fileHandle.createWritable();

      await writable.write(content);
      await writable.close();

      window.editor.markAsModified(false);

      this.currentFile = {
        name: filename,
        content: content,
        lastModified: Date.now(),
        directory: directoryHandle.name,
        handle: fileHandle
      };

      this.currentDirectory = {
        name: directoryHandle.name,
        handle: directoryHandle
      };

      this.addToRecentFiles(filename);
      this.addToRecentDirectories(directoryHandle.name);
      this.updateDirectoryInfo();

      alert(`File saved successfully to ${directoryHandle.name}/${filename}`);
    } catch (error) {
      if (error.name !== 'AbortError') {
        console.error('Error saving to directory:', error);
        throw error;
      }
    }
  }

  /**
   * Export to HTML
   */
  exportHtml() {
    const content = window.editor.getContent();
    const { metadata, content: markdownContent } = window.markdownRenderer.extractMetadata(content);
    const html = window.markdownRenderer.renderWithProcessing(markdownContent);
    
    const title = metadata.title || window.editor.currentFileName.replace(/\.md$/, '');
    const author = metadata.author || '';
    const description = metadata.description || '';
    
    const fullHtml = this.generateHtmlDocument(html, title, author, description);
    const filename = window.editor.currentFileName.replace(/\.md$/, '.html');
    
    EditorUtils.downloadFile(fullHtml, filename, 'text/html');
  }

  /**
   * Generate complete HTML document
   */
  generateHtmlDocument(content, title, author, description) {
    return `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${EditorUtils.escapeHtml(title)}</title>
  ${author ? `<meta name="author" content="${EditorUtils.escapeHtml(author)}">` : ''}
  ${description ? `<meta name="description" content="${EditorUtils.escapeHtml(description)}">` : ''}
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 800px;
      margin: 0 auto;
      padding: 2rem;
      background: #fff;
    }
    
    h1, h2, h3, h4, h5, h6 {
      margin-top: 1.5em;
      margin-bottom: 1em;
      line-height: 1.3;
      font-weight: 600;
    }
    
    h1 { font-size: 2em; border-bottom: 2px solid #eee; padding-bottom: 0.3em; }
    h2 { font-size: 1.5em; border-bottom: 1px solid #eee; padding-bottom: 0.3em; }
    h3 { font-size: 1.25em; }
    
    p { margin: 1em 0; }
    
    ul, ol { padding-left: 2em; margin: 1em 0; }
    li { margin: 0.25em 0; }
    
    blockquote {
      border-left: 4px solid #3498db;
      margin: 1em 0;
      padding: 0.5em 0 0.5em 1em;
      color: #666;
      background: #f8f9fa;
      border-radius: 0 4px 4px 0;
    }
    
    pre {
      background: #f6f8fa;
      padding: 1rem;
      border-radius: 6px;
      overflow-x: auto;
      border: 1px solid #e1e4e8;
      font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
      font-size: 0.9em;
      line-height: 1.4;
    }
    
    code {
      font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
      background: #f6f8fa;
      padding: 0.2em 0.4em;
      border-radius: 3px;
      font-size: 0.9em;
      border: 1px solid #e1e4e8;
    }
    
    pre code {
      background: transparent;
      padding: 0;
      border: none;
      border-radius: 0;
    }
    
    table {
      border-collapse: collapse;
      width: 100%;
      margin: 1em 0;
      border: 1px solid #ddd;
      border-radius: 6px;
      overflow: hidden;
    }
    
    th, td {
      border: 1px solid #ddd;
      padding: 0.5rem 1rem;
      text-align: left;
    }
    
    th {
      background: #f8f9fa;
      font-weight: 600;
    }
    
    tr:nth-child(even) {
      background: #f8f9fa;
    }
    
    img {
      max-width: 100%;
      height: auto;
      border-radius: 6px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
    
    a {
      color: #3498db;
      text-decoration: none;
    }
    
    a:hover {
      text-decoration: underline;
    }
    
    hr {
      border: none;
      border-top: 2px solid #eee;
      margin: 2em 0;
    }
    
    .header-link {
      opacity: 0;
      margin-left: 0.5em;
      font-weight: normal;
    }
    
    h1:hover .header-link,
    h2:hover .header-link,
    h3:hover .header-link,
    h4:hover .header-link,
    h5:hover .header-link,
    h6:hover .header-link {
      opacity: 1;
    }
    
    @media (max-width: 768px) {
      body {
        padding: 1rem;
      }
    }
    
    @media print {
      body {
        max-width: none;
        margin: 0;
        padding: 1rem;
      }
      
      .header-link {
        display: none;
      }
    }
  </style>
</head>
<body>
  ${content}
</body>
</html>`;
  }

  /**
   * Export to PDF (using browser print)
   */
  exportPdf() {
    const content = window.editor.getContent();
    const html = window.markdownRenderer.renderWithProcessing(content);
    
    // Create a new window with the content
    const printWindow = window.open('', '_blank');
    const title = window.editor.currentFileName.replace(/\.md$/, '');
    const fullHtml = this.generateHtmlDocument(html, title);
    
    printWindow.document.write(fullHtml);
    printWindow.document.close();
    
    // Wait for content to load, then print
    printWindow.onload = () => {
      printWindow.print();
    };
  }

  /**
   * Handle drag over
   */
  handleDragOver(event) {
    event.preventDefault();
    event.dataTransfer.dropEffect = 'copy';
  }

  /**
   * Handle directory selection (fallback method)
   */
  async handleDirectorySelect(event) {
    const files = Array.from(event.target.files);
    const markdownFiles = files.filter(file =>
      file.type === 'text/markdown' ||
      file.name.endsWith('.md') ||
      file.name.endsWith('.txt')
    );

    if (markdownFiles.length === 0) {
      alert('No markdown files found in the selected directory.');
      return;
    }

    // Show file selection dialog
    const selectedFile = await this.showFileSelectionDialogFallback(markdownFiles);
    if (selectedFile) {
      await this.loadFile(selectedFile);

      // Extract directory name from file path
      const pathParts = selectedFile.webkitRelativePath.split('/');
      if (pathParts.length > 1) {
        const directoryName = pathParts[0];
        this.currentDirectory = { name: directoryName };
        this.addToRecentDirectories(directoryName);
        this.updateDirectoryInfo();
      }
    }

    // Reset directory input
    this.directoryFileInput.value = '';
  }

  /**
   * Handle file drop
   */
  async handleDrop(event) {
    event.preventDefault();

    const files = Array.from(event.dataTransfer.files);
    const markdownFiles = files.filter(file =>
      file.type === 'text/markdown' ||
      file.name.endsWith('.md') ||
      file.name.endsWith('.txt')
    );

    if (markdownFiles.length > 0) {
      await this.loadFile(markdownFiles[0]);
    }
  }

  /**
   * Handle before unload
   */
  handleBeforeUnload(event) {
    if (window.editor.isModified) {
      const message = 'You have unsaved changes. Are you sure you want to leave?';
      event.returnValue = message;
      return message;
    }
  }

  /**
   * Add file to recent files list
   */
  addToRecentFiles(filename) {
    // Remove if already exists
    this.recentFiles = this.recentFiles.filter(file => file !== filename);
    
    // Add to beginning
    this.recentFiles.unshift(filename);
    
    // Limit to max recent files
    if (this.recentFiles.length > this.maxRecentFiles) {
      this.recentFiles = this.recentFiles.slice(0, this.maxRecentFiles);
    }
    
    this.saveRecentFiles();
  }

  /**
   * Load recent files from localStorage
   */
  loadRecentFiles() {
    try {
      const stored = localStorage.getItem('markdownEditor.recentFiles');
      if (stored) {
        this.recentFiles = JSON.parse(stored);
      }
    } catch (error) {
      console.warn('Failed to load recent files:', error);
      this.recentFiles = [];
    }
  }

  /**
   * Save recent files to localStorage
   */
  saveRecentFiles() {
    try {
      localStorage.setItem('markdownEditor.recentFiles', JSON.stringify(this.recentFiles));
    } catch (error) {
      console.warn('Failed to save recent files:', error);
    }
  }

  /**
   * Get recent files
   */
  getRecentFiles() {
    return [...this.recentFiles];
  }

  /**
   * Clear recent files
   */
  clearRecentFiles() {
    this.recentFiles = [];
    this.saveRecentFiles();
  }

  /**
   * Get current file info
   */
  getCurrentFileInfo() {
    return this.currentFile ? { ...this.currentFile } : null;
  }

  /**
   * Check if file has been modified externally
   */
  checkFileModification() {
    // This would be implemented for desktop applications
    // For web applications, this is not applicable
    return false;
  }

  /**
   * Get markdown files from directory handle
   */
  async getMarkdownFilesFromDirectory(directoryHandle) {
    const files = [];

    for await (const [name, handle] of directoryHandle.entries()) {
      if (handle.kind === 'file' && this.isMarkdownFile(name)) {
        files.push({ name, handle });
      }
    }

    return files.sort((a, b) => a.name.localeCompare(b.name));
  }

  /**
   * Check if file is a markdown file
   */
  isMarkdownFile(filename) {
    const extensions = ['.md', '.markdown', '.txt'];
    return extensions.some(ext => filename.toLowerCase().endsWith(ext));
  }

  /**
   * Show file selection dialog for modern API
   */
  async showFileSelectionDialog(files) {
    return new Promise((resolve) => {
      const modal = this.createFileSelectionModal(files, resolve);
      document.body.appendChild(modal);
    });
  }

  /**
   * Show file selection dialog for fallback method
   */
  async showFileSelectionDialogFallback(files) {
    return new Promise((resolve) => {
      const modal = this.createFileSelectionModal(files, resolve);
      document.body.appendChild(modal);
    });
  }

  /**
   * Create file selection modal
   */
  createFileSelectionModal(files, callback) {
    const modal = document.createElement('div');
    modal.className = 'modal file-selection-modal';
    modal.innerHTML = `
      <div class="modal-content">
        <div class="modal-header">
          <h3>Select File</h3>
          <button class="modal-close">&times;</button>
        </div>
        <div class="modal-body">
          <p>Choose a file to open:</p>
          <ul class="file-list">
            ${files.map((file, index) => `
              <li>
                <button class="file-item" data-index="${index}">
                  <i class="mdi mdi-file-document"></i>
                  <span>${file.name || file.name}</span>
                </button>
              </li>
            `).join('')}
          </ul>
        </div>
      </div>
    `;

    // Add event listeners
    const closeBtn = modal.querySelector('.modal-close');
    const fileItems = modal.querySelectorAll('.file-item');

    closeBtn.addEventListener('click', () => {
      document.body.removeChild(modal);
      callback(null);
    });

    fileItems.forEach((item, index) => {
      item.addEventListener('click', () => {
        document.body.removeChild(modal);
        callback(files[index]);
      });
    });

    // Close on background click
    modal.addEventListener('click', (e) => {
      if (e.target === modal) {
        document.body.removeChild(modal);
        callback(null);
      }
    });

    return modal;
  }

  /**
   * Setup directory modal
   */
  setupDirectoryModal() {
    const modal = document.getElementById('directory-modal');
    const selectBtn = document.getElementById('select-directory-btn');
    const closeBtn = modal?.querySelector('.modal-close');

    if (selectBtn) {
      selectBtn.addEventListener('click', () => {
        this.openFromDirectory();
        this.hideDirectoryModal();
      });
    }

    if (closeBtn) {
      closeBtn.addEventListener('click', () => {
        this.hideDirectoryModal();
      });
    }

    if (modal) {
      modal.addEventListener('click', (e) => {
        if (e.target === modal) {
          this.hideDirectoryModal();
        }
      });
    }

    this.updateRecentDirectoriesList();
  }

  /**
   * Show directory modal
   */
  showDirectoryModal() {
    const modal = document.getElementById('directory-modal');
    if (modal) {
      modal.style.display = 'flex';
      modal.setAttribute('aria-hidden', 'false');
    }
  }

  /**
   * Hide directory modal
   */
  hideDirectoryModal() {
    const modal = document.getElementById('directory-modal');
    if (modal) {
      modal.style.display = 'none';
      modal.setAttribute('aria-hidden', 'true');
    }
  }

  /**
   * Add directory to recent directories list
   */
  addToRecentDirectories(directoryName) {
    // Remove if already exists
    this.recentDirectories = this.recentDirectories.filter(dir => dir !== directoryName);

    // Add to beginning
    this.recentDirectories.unshift(directoryName);

    // Limit to max recent directories
    if (this.recentDirectories.length > this.maxRecentDirectories) {
      this.recentDirectories = this.recentDirectories.slice(0, this.maxRecentDirectories);
    }

    this.saveRecentDirectories();
    this.updateRecentDirectoriesList();
  }

  /**
   * Load recent directories from localStorage
   */
  loadRecentDirectories() {
    try {
      const stored = localStorage.getItem('markdownEditor.recentDirectories');
      if (stored) {
        this.recentDirectories = JSON.parse(stored);
      }
    } catch (error) {
      console.warn('Failed to load recent directories:', error);
      this.recentDirectories = [];
    }
  }

  /**
   * Save recent directories to localStorage
   */
  saveRecentDirectories() {
    try {
      localStorage.setItem('markdownEditor.recentDirectories', JSON.stringify(this.recentDirectories));
    } catch (error) {
      console.warn('Failed to save recent directories:', error);
    }
  }

  /**
   * Update recent directories list in UI
   */
  updateRecentDirectoriesList() {
    const list = document.getElementById('recent-directories-list');
    if (!list) return;

    list.innerHTML = '';

    if (this.recentDirectories.length === 0) {
      list.innerHTML = '<li class="empty">No recent directories</li>';
      return;
    }

    this.recentDirectories.forEach(dirName => {
      const li = document.createElement('li');
      li.innerHTML = `
        <button class="recent-directory-item">
          <i class="mdi mdi-folder"></i>
          <span>${dirName}</span>
        </button>
      `;

      const button = li.querySelector('.recent-directory-item');
      button.addEventListener('click', () => {
        // For now, just trigger the directory selection
        // In a full implementation, you might want to remember the actual directory handle
        this.openFromDirectory();
        this.hideDirectoryModal();
      });

      list.appendChild(li);
    });
  }

  /**
   * Get recent directories
   */
  getRecentDirectories() {
    return [...this.recentDirectories];
  }

  /**
   * Clear recent directories
   */
  clearRecentDirectories() {
    this.recentDirectories = [];
    this.saveRecentDirectories();
    this.updateRecentDirectoriesList();
  }

  /**
   * Get current directory info
   */
  getCurrentDirectoryInfo() {
    return this.currentDirectory ? { ...this.currentDirectory } : null;
  }

  /**
   * Update directory info in status bar
   */
  updateDirectoryInfo() {
    const directoryInfo = document.getElementById('directory-info');
    if (!directoryInfo) return;

    if (this.currentDirectory) {
      directoryInfo.textContent = `📁 ${this.currentDirectory.name}`;
      directoryInfo.style.display = 'inline';
      directoryInfo.title = `Current directory: ${this.currentDirectory.name}`;
    } else {
      directoryInfo.style.display = 'none';
    }
  }

  /**
   * Clear current directory
   */
  clearCurrentDirectory() {
    this.currentDirectory = null;
    this.updateDirectoryInfo();
  }
}

// Create global instance
window.fileManager = new FileManager();
