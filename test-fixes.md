# Test Document for Fixes

This document tests both the responsive layout fix and the KaTeX math rendering fix.

## Math Equations Test

### Inline Math
Here is some inline math: $E = mc^2$ and $F = ma$ and $\alpha + \beta = \gamma$.

### Display Math
Here is a display equation:

$$\int_{-\infty}^{\infty} e^{-x^2} dx = \sqrt{\pi}$$

### Complex Equations

Quadratic formula:
$$x = \frac{-b \pm \sqrt{b^2 - 4ac}}{2a}$$

E<PERSON><PERSON>'s identity:
$$e^{i\pi} + 1 = 0$$

Matrix:
$$\begin{pmatrix}
a & b \\
c & d
\end{pmatrix}$$

### Greek Letters and Symbols
$\alpha, \beta, \gamma, \delta, \epsilon, \zeta, \eta, \theta, \iota, \kappa, \lambda, \mu, \nu, \xi, \pi, \rho, \sigma, \tau, \upsilon, \phi, \chi, \psi, \omega$

### Fractions and Roots
Inline: $\frac{1}{2}$ and $\sqrt{x^2 + y^2}$ and $\sqrt[3]{8} = 2$

Display:
$$\frac{d}{dx}\left(\frac{1}{x}\right) = -\frac{1}{x^2}$$

### Summation and Integration
$$\sum_{n=1}^{\infty} \frac{1}{n^2} = \frac{\pi^2}{6}$$

$$\int_0^1 x^2 dx = \frac{1}{3}$$

### Limits
$$\lim_{x \to 0} \frac{\sin x}{x} = 1$$

$$\lim_{n \to \infty} \left(1 + \frac{1}{n}\right)^n = e$$

## Layout Test

This content should be visible without being blocked by the menu on medium and small screens.

### Regular Content
Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.

### More Math Mixed with Text
The equation $y = mx + b$ represents a linear function, while $y = ax^2 + bx + c$ represents a quadratic function. The derivative of $f(x) = x^n$ is $f'(x) = nx^{n-1}$.

For complex analysis, we often use:
$$f(z) = \sum_{n=0}^{\infty} a_n (z - z_0)^n$$

And the Cauchy integral formula:
$$f(z_0) = \frac{1}{2\pi i} \oint_C \frac{f(z)}{z - z_0} dz$$

## Test Instructions

1. **Layout Test**: Resize your browser window to medium (tablet) and small (mobile) sizes. The menu should not block the editor content.

2. **Math Test**: All the mathematical expressions above should render properly with KaTeX, showing mathematical symbols, fractions, integrals, summations, etc.

3. **Mixed Content Test**: Math should render correctly when mixed with regular text content.
