/**
 * Main Application Module
 * Initializes and coordinates all modules
 */

class MarkdownEditorApp {
  constructor() {
    this.version = '2.0.0';
    this.modules = new Map();
    this.settings = {
      theme: 'auto',
      fontSize: 16,
      lineHeight: 1.6,
      wordWrap: true,
      showLineNumbers: false,
      autoSave: false,
      autoSaveInterval: 30000, // 30 seconds
      spellCheck: false,
      previewSync: true
    };
    
    this.initialize();
  }

  /**
   * Initialize the application
   */
  initialize() {
    // Wait for DOM to be ready
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.start());
    } else {
      this.start();
    }
  }

  /**
   * Start the application
   */
  start() {
    try {
      // Check for required dependencies
      this.checkDependencies();

      this.loadSettings();
      this.initializeModules();
      this.setupGlobalEventListeners();
      this.setupErrorHandling();
      this.applySettings();
      this.showWelcomeMessage();

      console.log(`Markdown Editor v${this.version} initialized successfully`);
    } catch (error) {
      console.error('Failed to initialize application:', error);
      this.showErrorMessage('Failed to initialize the application. Please refresh the page.');
    }
  }

  /**
   * Check for required dependencies
   */
  checkDependencies() {
    const dependencies = [
      { name: 'marked', global: 'marked' },
      { name: 'highlight.js', global: 'hljs' },
      { name: 'KaTeX', global: 'katex' },
      { name: 'KaTeX auto-render', global: 'renderMathInElement' }
    ];

    const missing = dependencies.filter(dep => typeof window[dep.global] === 'undefined');

    if (missing.length > 0) {
      const missingNames = missing.map(dep => dep.name).join(', ');
      console.warn(`Missing dependencies: ${missingNames}`);

      // Show user-friendly message
      this.showErrorMessage(`Some features may not work properly. Missing: ${missingNames}`);
    } else {
      console.log('All dependencies loaded successfully');
    }
  }

  /**
   * Initialize all modules
   */
  initializeModules() {
    // Modules are already initialized by their respective scripts
    // Store references for easy access
    this.modules.set('utils', window.EditorUtils);
    this.modules.set('renderer', window.markdownRenderer);
    this.modules.set('editor', window.editor);
    this.modules.set('toolbar', window.toolbar);
    this.modules.set('fileManager', window.fileManager);
    this.modules.set('katexIntegration', window.katexIntegration);
    this.modules.set('mermaidIntegration', window.mermaidIntegration);

    // Verify all modules are loaded
    const requiredModules = ['utils', 'renderer', 'editor', 'toolbar', 'fileManager'];
    const missingModules = requiredModules.filter(name => !this.modules.get(name));

    if (missingModules.length > 0) {
      throw new Error(`Missing required modules: ${missingModules.join(', ')}`);
    }

    // Check if KaTeX integration is available
    if (this.modules.get('katexIntegration') && this.modules.get('katexIntegration').isAvailable()) {
      console.log('KaTeX integration available');
    } else {
      console.warn('KaTeX integration not available - math rendering will be limited');
    }
  }

  /**
   * Setup global event listeners
   */
  setupGlobalEventListeners() {
    // Handle window resize
    window.addEventListener('resize', EditorUtils.debounce(() => {
      this.handleWindowResize();
    }, 250));

    // Handle visibility change (for auto-save)
    document.addEventListener('visibilitychange', () => {
      if (document.hidden && this.settings.autoSave && window.editor.isModified) {
        this.autoSave();
      }
    });

    // Handle online/offline status
    window.addEventListener('online', () => this.handleOnlineStatus(true));
    window.addEventListener('offline', () => this.handleOnlineStatus(false));

    // Handle theme changes
    if (window.matchMedia) {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      mediaQuery.addEventListener('change', () => this.handleThemeChange());
    }
  }

  /**
   * Setup error handling
   */
  setupErrorHandling() {
    // Global error handler
    window.addEventListener('error', (event) => {
      console.error('Global error:', event.error);
      this.handleError(event.error);
    });

    // Unhandled promise rejection handler
    window.addEventListener('unhandledrejection', (event) => {
      console.error('Unhandled promise rejection:', event.reason);
      this.handleError(event.reason);
    });
  }

  /**
   * Load settings from localStorage
   */
  loadSettings() {
    try {
      const stored = localStorage.getItem('markdownEditor.settings');
      if (stored) {
        const parsedSettings = JSON.parse(stored);
        this.settings = { ...this.settings, ...parsedSettings };
      }
    } catch (error) {
      console.warn('Failed to load settings:', error);
    }
  }

  /**
   * Save settings to localStorage
   */
  saveSettings() {
    try {
      localStorage.setItem('markdownEditor.settings', JSON.stringify(this.settings));
    } catch (error) {
      console.warn('Failed to save settings:', error);
    }
  }

  /**
   * Apply current settings
   */
  applySettings() {
    const editor = window.editor.editorElement;
    if (!editor) return;

    // Apply font size
    editor.style.fontSize = `${this.settings.fontSize}px`;
    
    // Apply line height
    editor.style.lineHeight = this.settings.lineHeight;
    
    // Apply word wrap
    editor.style.whiteSpace = this.settings.wordWrap ? 'pre-wrap' : 'pre';
    
    // Apply spell check
    editor.spellcheck = this.settings.spellCheck;
    
    // Apply theme
    this.applyTheme(this.settings.theme);
    
    // Setup auto-save
    if (this.settings.autoSave) {
      this.setupAutoSave();
    }
  }

  /**
   * Apply theme
   */
  applyTheme(theme) {
    const body = document.body;
    body.classList.remove('theme-light', 'theme-dark');
    
    if (theme === 'light') {
      body.classList.add('theme-light');
    } else if (theme === 'dark') {
      body.classList.add('theme-dark');
    }
    // 'auto' theme uses CSS media queries
  }

  /**
   * Setup auto-save
   */
  setupAutoSave() {
    if (this.autoSaveTimer) {
      clearInterval(this.autoSaveTimer);
    }
    
    if (this.settings.autoSave) {
      this.autoSaveTimer = setInterval(() => {
        if (window.editor.isModified) {
          this.autoSave();
        }
      }, this.settings.autoSaveInterval);
    }
  }

  /**
   * Auto-save current content
   */
  autoSave() {
    try {
      const content = window.editor.getContent();
      const filename = window.editor.currentFileName;
      
      // Save to localStorage as backup
      localStorage.setItem('markdownEditor.autoSave', JSON.stringify({
        content,
        filename,
        timestamp: Date.now()
      }));
      
      console.log('Auto-saved content');
    } catch (error) {
      console.warn('Auto-save failed:', error);
    }
  }

  /**
   * Restore auto-saved content
   */
  restoreAutoSave() {
    try {
      const stored = localStorage.getItem('markdownEditor.autoSave');
      if (stored) {
        const { content, filename, timestamp } = JSON.parse(stored);
        
        // Only restore if it's recent (within 24 hours)
        const hoursSinceAutoSave = (Date.now() - timestamp) / (1000 * 60 * 60);
        if (hoursSinceAutoSave < 24) {
          const shouldRestore = EditorUtils.showConfirmDialog(
            `Found auto-saved content from ${new Date(timestamp).toLocaleString()}. Do you want to restore it?`
          );
          
          if (shouldRestore) {
            window.editor.setContent(content);
            window.editor.setFileName(filename);
            window.editor.markAsModified(true);
          }
        }
        
        // Clear the auto-save after handling
        localStorage.removeItem('markdownEditor.autoSave');
      }
    } catch (error) {
      console.warn('Failed to restore auto-save:', error);
    }
  }

  /**
   * Handle window resize
   */
  handleWindowResize() {
    // Update editor layout if needed
    // This is a placeholder for responsive adjustments
  }

  /**
   * Handle online/offline status
   */
  handleOnlineStatus(isOnline) {
    const statusIndicator = document.querySelector('.status-bar');
    if (statusIndicator) {
      if (isOnline) {
        statusIndicator.classList.remove('offline');
      } else {
        statusIndicator.classList.add('offline');
        // Auto-save when going offline
        if (window.editor.isModified) {
          this.autoSave();
        }
      }
    }
  }

  /**
   * Handle theme change
   */
  handleThemeChange() {
    if (this.settings.theme === 'auto') {
      // Re-apply auto theme to pick up system changes
      this.applyTheme('auto');
    }
  }

  /**
   * Handle errors
   */
  handleError(error) {
    // Log error details
    console.error('Application error:', error);
    
    // Show user-friendly error message
    this.showErrorMessage('An error occurred. Please check the console for details.');
  }

  /**
   * Show welcome message
   */
  showWelcomeMessage() {
    // Check if this is the first visit
    const hasVisited = localStorage.getItem('markdownEditor.hasVisited');
    if (!hasVisited) {
      localStorage.setItem('markdownEditor.hasVisited', 'true');
      
      // Show welcome message after a short delay
      setTimeout(() => {
        console.log('Welcome to Markdown Editor! Press F1 for help.');
      }, 1000);
    }
    
    // Check for auto-save restore
    this.restoreAutoSave();
  }

  /**
   * Show error message
   */
  showErrorMessage(message) {
    // Create error notification
    const notification = document.createElement('div');
    notification.className = 'error-notification';
    notification.textContent = message;
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: #e74c3c;
      color: white;
      padding: 1rem;
      border-radius: 4px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      z-index: 10000;
      max-width: 300px;
    `;
    
    document.body.appendChild(notification);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 5000);
  }

  /**
   * Show success message
   */
  showSuccessMessage(message) {
    // Create success notification
    const notification = document.createElement('div');
    notification.className = 'success-notification';
    notification.textContent = message;
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: #27ae60;
      color: white;
      padding: 1rem;
      border-radius: 4px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      z-index: 10000;
      max-width: 300px;
    `;
    
    document.body.appendChild(notification);
    
    // Auto-remove after 3 seconds
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 3000);
  }

  /**
   * Get application info
   */
  getInfo() {
    return {
      version: this.version,
      modules: Array.from(this.modules.keys()),
      settings: { ...this.settings }
    };
  }

  /**
   * Update setting
   */
  updateSetting(key, value) {
    if (key in this.settings) {
      this.settings[key] = value;
      this.saveSettings();
      this.applySettings();
      return true;
    }
    return false;
  }

  /**
   * Reset settings to defaults
   */
  resetSettings() {
    const defaultSettings = {
      theme: 'auto',
      fontSize: 16,
      lineHeight: 1.6,
      wordWrap: true,
      showLineNumbers: false,
      autoSave: false,
      autoSaveInterval: 30000,
      spellCheck: false,
      previewSync: true
    };
    
    this.settings = { ...defaultSettings };
    this.saveSettings();
    this.applySettings();
  }

  /**
   * Cleanup on page unload
   */
  cleanup() {
    if (this.autoSaveTimer) {
      clearInterval(this.autoSaveTimer);
    }
  }
}

// Initialize the application
window.markdownEditorApp = new MarkdownEditorApp();

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
  window.markdownEditorApp.cleanup();
});

// Expose app globally for debugging
window.app = window.markdownEditorApp;
